package utils

import (
	"bytes"
	"context"
	"fmt"
	"log"
	"net/http"
	"text/template"
	"time"
)

// =============================================================================
// 飞书通知器实现
// =============================================================================

// FeishuConfig 飞书配置
type FeishuConfig struct {
	WebhookURL string            `json:"webhook_url"`
	Enabled    bool              `json:"enabled"`
	UserMap    map[string]string `json:"user_map"`
	Templates  map[string]string `json:"templates"`
	Timeout    time.Duration     `json:"timeout"` // HTTP请求超时时间
}

// FeishuNotifier 飞书通知器
type FeishuNotifier struct {
	config FeishuConfig
}

// NewFeishuNotifier 创建飞书通知器
func NewFeishuNotifier(config FeishuConfig) *FeishuNotifier {
	// 设置默认超时时间
	if config.Timeout == 0 {
		config.Timeout = 10 * time.Second
	}

	return &FeishuNotifier{config: config}
}

// SendNotification 发送通知
func (f *FeishuNotifier) SendNotification(ctx context.Context, message *NotificationMessage) error {
	if !f.IsEnabled() {
		log.Printf("Feishu notifier is disabled, skipping notification")
		return nil // 静默跳过
	}

	// 根据消息级别选择模板
	templateStr := f.getTemplate(message.Level)

	// 渲染消息
	content, err := f.renderMessage(templateStr, message)
	if err != nil {
		return fmt.Errorf("failed to render feishu message: %w", err)
	}

	// 发送HTTP请求
	return f.sendHTTPRequest(ctx, content)
}

// GetNotifierType 获取通知器类型
func (f *FeishuNotifier) GetNotifierType() string {
	return "feishu"
}

// IsEnabled 检查是否启用
func (f *FeishuNotifier) IsEnabled() bool {
	return f.config.Enabled && f.config.WebhookURL != ""
}

// getTemplate 根据消息级别获取模板
func (f *FeishuNotifier) getTemplate(level NotificationLevel) string {
	levelStr := string(level)
	if template, exists := f.config.Templates[levelStr]; exists {
		return template
	}
	// 返回默认模板
	return f.getDefaultTemplate(level)
}

// getDefaultTemplate 获取默认模板
func (f *FeishuNotifier) getDefaultTemplate(level NotificationLevel) string {
	switch level {
	case NotificationLevelError:
		return f.getServiceFailedTemplate()
	case NotificationLevelSuccess:
		return f.getServiceSuccessTemplate()
	case NotificationLevelInfo, NotificationLevelWarning:
		return f.getGenericTemplate()
	default:
		return f.getGenericTemplate()
	}
}

// getServiceFailedTemplate 获取服务失败模板
func (f *FeishuNotifier) getServiceFailedTemplate() string {
	return `{
		"msg_type": "post",
		"content": {
			"post": {
				"zh_cn": {
					"title": "🚨🚨🚨 {{.Title}} By zero-ops-platform_app-deploy",
					"content": [
						[
							{
								"tag": "text",
								"text": "服务ID: {{.ServiceID}}"
							}
						],
						[
							{
								"tag": "text",
								"text": "级别: {{.Level}}"
							}
						],
						[
							{
								"tag": "text",
								"text": "详情: {{.Content}}"
							}
						],
						[
							{
								"tag": "text",
								"text": "时间: {{.Timestamp}}"
							}
						]
					]
				}
			}
		}
	}`
}

// getServiceSuccessTemplate 获取服务成功模板
func (f *FeishuNotifier) getServiceSuccessTemplate() string {
	return `{
		"msg_type": "post",
		"content": {
			"post": {
				"zh_cn": {
					"title": "✅ {{.Title}} By zero-ops-platform_app-deploy",
					"content": [
						[
							{
								"tag": "text",
								"text": "服务ID: {{.ServiceID}}"
							}
						],
						[
							{
								"tag": "text",
								"text": "级别: {{.Level}}"
							}
						],
						[
							{
								"tag": "text",
								"text": "详情: {{.Content}}"
							}
						],
						[
							{
								"tag": "text",
								"text": "时间: {{.Timestamp}}"
							}
						]
					]
				}
			}
		}
	}`
}

// getGenericTemplate 获取通用模板
func (f *FeishuNotifier) getGenericTemplate() string {
	return `{
		"msg_type": "post",
		"content": {
			"post": {
				"zh_cn": {
					"title": "📢 {{.Title}} By zero-ops-platform_app-deploy",
					"content": [
						[
							{
								"tag": "text",
								"text": "服务ID: {{.ServiceID}}"
							}
						],
						[
							{
								"tag": "text",
								"text": "级别: {{.Level}}"
							}
						],
						[
							{
								"tag": "text",
								"text": "详情: {{.Content}}"
							}
						],
						[
							{
								"tag": "text",
								"text": "时间: {{.Timestamp}}"
							}
						]
					]
				}
			}
		}
	}`
}

// renderMessage 渲染消息模板
func (f *FeishuNotifier) renderMessage(templateStr string, message *NotificationMessage) ([]byte, error) {
	tmpl, err := template.New("feishu").Parse(templateStr)
	if err != nil {
		return nil, fmt.Errorf("failed to parse template: %w", err)
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, message); err != nil {
		return nil, fmt.Errorf("failed to execute template: %w", err)
	}

	return buf.Bytes(), nil
}

// sendHTTPRequest 发送HTTP请求
func (f *FeishuNotifier) sendHTTPRequest(ctx context.Context, content []byte) error {
	// 创建带超时的context
	ctx, cancel := context.WithTimeout(ctx, f.config.Timeout)
	defer cancel()

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", f.config.WebhookURL, bytes.NewReader(content))
	if err != nil {
		return fmt.Errorf("failed to create HTTP request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json; charset=UTF-8")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send HTTP request: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("feishu webhook returned status code: %d", resp.StatusCode)
	}

	log.Printf("Successfully sent feishu notification for message type: %s", "notification")
	return nil
}
