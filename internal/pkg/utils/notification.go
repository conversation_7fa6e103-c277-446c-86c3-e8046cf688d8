package utils

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"
)

// =============================================================================
// 通知级别和消息结构定义
// =============================================================================

// NotificationLevel 通知级别
type NotificationLevel string

const (
	NotificationLevelInfo    NotificationLevel = "INFO"
	NotificationLevelWarning NotificationLevel = "WARNING"
	NotificationLevelError   NotificationLevel = "ERROR"
	NotificationLevelSuccess NotificationLevel = "SUCCESS"
)

// NotificationMessage 通知消息结构
type NotificationMessage struct {
	Level     NotificationLevel      `json:"level"`
	Title     string                 `json:"title"`
	Content   string                 `json:"content"`
	ServiceID string                 `json:"service_id"`
	Metadata  map[string]interface{} `json:"metadata"`
	Timestamp string                 `json:"timestamp"` // 东八区时间格式
}

// =============================================================================
// 通知器接口定义
// =============================================================================

// Notifier 通知器接口
type Notifier interface {
	// SendNotification 发送通知
	SendNotification(ctx context.Context, message *NotificationMessage) error

	// GetNotifierType 获取通知器类型
	GetNotifierType() string

	// IsEnabled 检查是否启用
	IsEnabled() bool
}

// =============================================================================
// 通知管理器实现
// =============================================================================

// NotificationManager 通知管理器
type NotificationManager struct {
	notifiers []Notifier
	mu        sync.RWMutex

	// 通知去重管理
	deduplicationCache map[string]time.Time // key: "{service_id}#{title}", value: 最后通知时间
	deduplicationMu    sync.RWMutex         // 保护去重缓存的并发访问
	defaultCooldown    time.Duration        // 默认冷却时间
}

// NewNotificationManager 创建通知管理器
func NewNotificationManager() *NotificationManager {
	return &NotificationManager{
		notifiers:          make([]Notifier, 0),
		deduplicationCache: make(map[string]time.Time),
		defaultCooldown:    30 * time.Minute, // 默认30分钟冷却期
	}
}

// RegisterNotifier 注册通知器
func (nm *NotificationManager) RegisterNotifier(notifier Notifier) {
	nm.mu.Lock()
	defer nm.mu.Unlock()

	nm.notifiers = append(nm.notifiers, notifier)
	log.Printf("Registered notifier: %s (enabled: %v)", notifier.GetNotifierType(), notifier.IsEnabled())
}

// =============================================================================
// 通知去重功能
// =============================================================================

// generateDeduplicationKey 生成去重Key
func (nm *NotificationManager) generateDeduplicationKey(serviceID, title string) string {
	// 使用 # 作为分隔符，因为service_id和title中不太可能包含此字符
	return fmt.Sprintf("%s#%s", serviceID, title)
}

// shouldSendNotification 检查是否应该发送通知（基于去重逻辑）
func (nm *NotificationManager) shouldSendNotification(serviceID, title string, cooldown time.Duration) bool {
	key := nm.generateDeduplicationKey(serviceID, title)

	nm.deduplicationMu.RLock()
	defer nm.deduplicationMu.RUnlock()

	lastNotified, exists := nm.deduplicationCache[key]
	if !exists {
		return true // 从未通知过此类型
	}

	// 检查是否超过冷却期
	return time.Since(lastNotified) > cooldown
}

// recordNotification 记录通知时间
func (nm *NotificationManager) recordNotification(serviceID, title string) {
	key := nm.generateDeduplicationKey(serviceID, title)

	nm.deduplicationMu.Lock()
	defer nm.deduplicationMu.Unlock()

	nm.deduplicationCache[key] = time.Now()
}

// getRemainingCooldown 获取剩余冷却时间（用于日志）
func (nm *NotificationManager) getRemainingCooldown(serviceID, title string, cooldown time.Duration) time.Duration {
	key := nm.generateDeduplicationKey(serviceID, title)

	nm.deduplicationMu.RLock()
	defer nm.deduplicationMu.RUnlock()

	lastNotified, exists := nm.deduplicationCache[key]
	if !exists {
		return 0
	}

	elapsed := time.Since(lastNotified)
	if elapsed >= cooldown {
		return 0
	}

	return cooldown - elapsed
}

// SendNotification 发送通知到所有启用的通知器
func (nm *NotificationManager) SendNotification(ctx context.Context, message *NotificationMessage) error {
	nm.mu.RLock()
	defer nm.mu.RUnlock()

	if len(nm.notifiers) == 0 {
		log.Printf("No notifiers registered, skipping notification")
		return nil
	}

	var lastError error
	successCount := 0
	enabledCount := 0

	for _, notifier := range nm.notifiers {
		if !notifier.IsEnabled() {
			log.Printf("Notifier %s is disabled, skipping", notifier.GetNotifierType())
			continue
		}

		enabledCount++

		if err := notifier.SendNotification(ctx, message); err != nil {
			log.Printf("Failed to send notification via %s: %v", notifier.GetNotifierType(), err)
			lastError = err
		} else {
			successCount++
			log.Printf("Successfully sent notification via %s for service %s",
				notifier.GetNotifierType(), message.ServiceID)
		}
	}

	// 记录发送统计
	log.Printf("Notification sent: %d/%d enabled notifiers succeeded", successCount, enabledCount)

	// 如果没有启用的通知器，返回提示
	if enabledCount == 0 {
		return fmt.Errorf("no enabled notifiers available")
	}

	// 如果所有启用的通知器都失败了，返回最后一个错误
	if successCount == 0 && lastError != nil {
		return fmt.Errorf("all notifiers failed, last error: %w", lastError)
	}

	return nil
}

// SendNotificationWithDeduplication 发送通知（带去重功能）
func (nm *NotificationManager) SendNotificationWithDeduplication(ctx context.Context, message *NotificationMessage, cooldown time.Duration) error {
	// 检查是否应该发送通知
	if !nm.shouldSendNotification(message.ServiceID, message.Title, cooldown) {
		log.Printf("🔇 跳过重复通知: %s (冷却期内，剩余时间: %v)",
			nm.generateDeduplicationKey(message.ServiceID, message.Title),
			nm.getRemainingCooldown(message.ServiceID, message.Title, cooldown))
		return nil
	}

	// 发送通知
	err := nm.SendNotification(ctx, message)
	if err != nil {
		return err
	}

	// 记录通知时间
	nm.recordNotification(message.ServiceID, message.Title)

	log.Printf("📢 发送通知成功: %s", nm.generateDeduplicationKey(message.ServiceID, message.Title))
	return nil
}

// SendNotificationWithDefaultCooldown 使用默认冷却时间发送通知
func (nm *NotificationManager) SendNotificationWithDefaultCooldown(ctx context.Context, message *NotificationMessage) error {
	return nm.SendNotificationWithDeduplication(ctx, message, nm.defaultCooldown)
}

// SendServiceFailedNotification 发送服务失败通知的便捷方法
func (nm *NotificationManager) SendServiceFailedNotification(ctx context.Context, serviceID, serviceName, imageName, workerID, nodeIP, failureReason string) error {
	message := &NotificationMessage{
		Level:     NotificationLevelError,
		Title:     "服务部署失败",
		Content:   fmt.Sprintf("服务 %s (%s) 部署失败，请检查相关配置和资源", serviceName, serviceID),
		ServiceID: serviceID,
		Metadata: map[string]interface{}{
			"service_name":   serviceName,
			"image_name":     imageName,
			"worker_id":      workerID,
			"node_ip":        nodeIP,
			"failure_reason": failureReason,
		},
		Timestamp: GetCSTTimeString(), // 使用东八区时间
	}

	return nm.SendNotification(ctx, message)
}

// SendServiceSuccessNotification 发送服务成功通知的便捷方法
func (nm *NotificationManager) SendServiceSuccessNotification(ctx context.Context, serviceID, serviceName, content string) error {
	message := &NotificationMessage{
		Level:     NotificationLevelSuccess,
		Title:     "服务操作成功",
		Content:   content,
		ServiceID: serviceID,
		Metadata: map[string]interface{}{
			"service_name": serviceName,
		},
		Timestamp: GetCSTTimeString(), // 使用东八区时间
	}

	return nm.SendNotification(ctx, message)
}

// SendCustomNotification 发送自定义通知的便捷方法
func (nm *NotificationManager) SendCustomNotification(ctx context.Context, level NotificationLevel, title, content, serviceID string, metadata map[string]interface{}) error {
	message := &NotificationMessage{
		Level:     level,
		Title:     title,
		Content:   content,
		ServiceID: serviceID,
		Metadata:  metadata,
		Timestamp: GetCSTTimeString(), // 使用东八区时间
	}

	return nm.SendNotification(ctx, message)
}

// =============================================================================
// 带去重功能的便捷方法
// =============================================================================

// SendServiceFailedNotificationWithDeduplication 发送服务失败通知（带去重功能）
func (nm *NotificationManager) SendServiceFailedNotificationWithDeduplication(ctx context.Context, serviceID, serviceName, imageName, workerID, nodeIP, failureReason string, cooldown time.Duration) error {
	message := &NotificationMessage{
		Level:     NotificationLevelError,
		Title:     "服务部署失败", // 固定的title，便于去重
		Content:   fmt.Sprintf("服务 %s (%s) 部署失败，请检查相关配置和资源", serviceName, serviceID),
		ServiceID: serviceID,
		Metadata: map[string]interface{}{
			"service_name":   serviceName,
			"image_name":     imageName,
			"worker_id":      workerID,
			"node_ip":        nodeIP,
			"failure_reason": failureReason,
		},
		Timestamp: GetCSTTimeString(),
	}

	return nm.SendNotificationWithDeduplication(ctx, message, cooldown)
}

// SendServiceSuccessNotificationWithDeduplication 发送服务成功通知（带去重功能）
func (nm *NotificationManager) SendServiceSuccessNotificationWithDeduplication(ctx context.Context, serviceID, serviceName, content string, cooldown time.Duration) error {
	message := &NotificationMessage{
		Level:     NotificationLevelSuccess,
		Title:     "服务操作成功", // 固定的title，便于去重
		Content:   content,
		ServiceID: serviceID,
		Metadata: map[string]interface{}{
			"service_name": serviceName,
		},
		Timestamp: GetCSTTimeString(),
	}

	return nm.SendNotificationWithDeduplication(ctx, message, cooldown)
}

// SendCustomNotificationWithDeduplication 发送自定义通知（带去重功能）
func (nm *NotificationManager) SendCustomNotificationWithDeduplication(ctx context.Context, level NotificationLevel, title, content, serviceID string, metadata map[string]interface{}, cooldown time.Duration) error {
	message := &NotificationMessage{
		Level:     level,
		Title:     title,
		Content:   content,
		ServiceID: serviceID,
		Metadata:  metadata,
		Timestamp: GetCSTTimeString(),
	}

	return nm.SendNotificationWithDeduplication(ctx, message, cooldown)
}

// GetEnabledNotifiers 获取启用的通知器列表
func (nm *NotificationManager) GetEnabledNotifiers() []string {
	nm.mu.RLock()
	defer nm.mu.RUnlock()

	var enabled []string
	for _, notifier := range nm.notifiers {
		if notifier.IsEnabled() {
			enabled = append(enabled, notifier.GetNotifierType())
		}
	}
	return enabled
}

// GetNotifierCount 获取通知器数量统计
func (nm *NotificationManager) GetNotifierCount() (total, enabled int) {
	nm.mu.RLock()
	defer nm.mu.RUnlock()

	total = len(nm.notifiers)
	for _, notifier := range nm.notifiers {
		if notifier.IsEnabled() {
			enabled++
		}
	}
	return total, enabled
}

// SetNotifierEnabled 设置通知器启用状态（如果支持动态配置）
func (nm *NotificationManager) SetNotifierEnabled(notifierType string, enabled bool) error {
	nm.mu.Lock()
	defer nm.mu.Unlock()

	for _, notifier := range nm.notifiers {
		if notifier.GetNotifierType() == notifierType {
			// 这里需要通知器支持动态配置，当前实现中暂时不支持
			// 可以在未来扩展时添加 SetEnabled 方法到 Notifier 接口
			log.Printf("Found notifier %s, but dynamic configuration not supported yet", notifierType)
			return fmt.Errorf("dynamic configuration not supported for notifier: %s", notifierType)
		}
	}

	return fmt.Errorf("notifier not found: %s", notifierType)
}

// =============================================================================
// 全局配置和默认创建函数
// =============================================================================

// NotificationConfig 通知配置结构（避免循环导入）
type NotificationConfig struct {
	Feishu   FeishuNotificationConfig   `yaml:"feishu" json:"feishu"`
	WeChat   WeChatNotificationConfig   `yaml:"wechat" json:"wechat"`
	DingTalk DingTalkNotificationConfig `yaml:"dingtalk" json:"dingtalk"`
	Email    EmailNotificationConfig    `yaml:"email" json:"email"`
}

// FeishuNotificationConfig 飞书通知配置
type FeishuNotificationConfig struct {
	Enabled    bool              `yaml:"enabled" json:"enabled"`
	WebhookURL string            `yaml:"webhook_url" json:"webhook_url"`
	UserMap    map[string]string `yaml:"user_map" json:"user_map"`
}

// WeChatNotificationConfig 微信通知配置
type WeChatNotificationConfig struct {
	Enabled    bool   `yaml:"enabled" json:"enabled"`
	WebhookURL string `yaml:"webhook_url" json:"webhook_url"`
	AppID      string `yaml:"app_id" json:"app_id"`
	Secret     string `yaml:"secret" json:"secret"`
}

// DingTalkNotificationConfig 钉钉通知配置
type DingTalkNotificationConfig struct {
	Enabled     bool   `yaml:"enabled" json:"enabled"`
	WebhookURL  string `yaml:"webhook_url" json:"webhook_url"`
	AccessToken string `yaml:"access_token" json:"access_token"`
}

// EmailNotificationConfig 邮件通知配置
type EmailNotificationConfig struct {
	Enabled     bool     `yaml:"enabled" json:"enabled"`
	SMTPHost    string   `yaml:"smtp_host" json:"smtp_host"`
	SMTPPort    int      `yaml:"smtp_port" json:"smtp_port"`
	Username    string   `yaml:"username" json:"username"`
	Password    string   `yaml:"password" json:"password"`
	FromName    string   `yaml:"from_name" json:"from_name"`
	ToAddresses []string `yaml:"to_addresses" json:"to_addresses"`
}

// CreateNotificationManagerFromConfig 基于通知配置创建通知管理器
func CreateNotificationManagerFromConfig(notificationConfig NotificationConfig) *NotificationManager {
	manager := NewNotificationManager()

	// 🔍 根据配置决定是否启用飞书通知
	if notificationConfig.Feishu.Enabled {
		feishuConfig := FeishuConfig{
			WebhookURL: notificationConfig.Feishu.WebhookURL,
			Enabled:    notificationConfig.Feishu.Enabled,
			UserMap:    notificationConfig.Feishu.UserMap,
			Templates:  make(map[string]string),
			Timeout:    10 * time.Second,
		}
		feishuNotifier := NewFeishuNotifier(feishuConfig)
		manager.RegisterNotifier(feishuNotifier)
		log.Printf("✅ Feishu notifier enabled from config file")
	} else {
		log.Printf("❌ Feishu notifier disabled by config")
	}

	// 🔍 微信、钉钉、邮件通知器暂未实现，先记录配置状态
	if notificationConfig.WeChat.Enabled {
		log.Printf("⚠️  WeChat notifier enabled in config but not implemented yet")
	} else {
		log.Printf("❌ WeChat notifier disabled by config")
	}

	if notificationConfig.DingTalk.Enabled {
		log.Printf("⚠️  DingTalk notifier enabled in config but not implemented yet")
	} else {
		log.Printf("❌ DingTalk notifier disabled by config")
	}

	if notificationConfig.Email.Enabled {
		log.Printf("⚠️  Email notifier enabled in config but not implemented yet")
	} else {
		log.Printf("❌ Email notifier disabled by config")
	}

	// 启动缓存清理
	manager.startCacheCleanup()

	log.Printf("✅ NotificationManager initialized with deduplication (default cooldown: %v)", manager.defaultCooldown)
	return manager
}

// =============================================================================
// 缓存清理机制
// =============================================================================

// cleanupExpiredCache 清理过期的缓存条目
func (nm *NotificationManager) cleanupExpiredCache() {
	nm.deduplicationMu.Lock()
	defer nm.deduplicationMu.Unlock()

	now := time.Now()
	maxAge := nm.defaultCooldown * 2 // 保留时间为冷却时间的2倍

	cleanedCount := 0
	for key, lastNotified := range nm.deduplicationCache {
		if now.Sub(lastNotified) > maxAge {
			delete(nm.deduplicationCache, key)
			cleanedCount++
		}
	}

	if cleanedCount > 0 {
		log.Printf("🧹 清理过期通知缓存: 删除 %d 条记录，当前缓存条目数: %d", cleanedCount, len(nm.deduplicationCache))
	}
}

// startCacheCleanup 启动定期缓存清理
func (nm *NotificationManager) startCacheCleanup() {
	ticker := time.NewTicker(1 * time.Hour) // 每小时清理一次

	go func() {
		for range ticker.C {
			nm.cleanupExpiredCache()
		}
	}()

	log.Printf("🔄 启动通知缓存清理任务，清理间隔: 1小时")
}
