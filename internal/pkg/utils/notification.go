package utils

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"
)

// =============================================================================
// 通知级别和消息结构定义
// =============================================================================

// NotificationLevel 通知级别
type NotificationLevel string

const (
	NotificationLevelInfo    NotificationLevel = "INFO"
	NotificationLevelWarning NotificationLevel = "WARNING"
	NotificationLevelError   NotificationLevel = "ERROR"
	NotificationLevelSuccess NotificationLevel = "SUCCESS"
)

// NotificationMessage 通知消息结构
type NotificationMessage struct {
	Level     NotificationLevel      `json:"level"`
	Title     string                 `json:"title"`
	Content   string                 `json:"content"`
	ServiceID string                 `json:"service_id"`
	Metadata  map[string]interface{} `json:"metadata"`
	Timestamp string                 `json:"timestamp"` // 东八区时间格式
}

// =============================================================================
// 通知器接口定义
// =============================================================================

// Notifier 通知器接口
type Notifier interface {
	// SendNotification 发送通知
	SendNotification(ctx context.Context, message *NotificationMessage) error

	// GetNotifierType 获取通知器类型
	GetNotifierType() string

	// IsEnabled 检查是否启用
	IsEnabled() bool
}

// =============================================================================
// 通知管理器实现
// =============================================================================

// NotificationManager 通知管理器
type NotificationManager struct {
	notifiers []Notifier
	mu        sync.RWMutex
}

// NewNotificationManager 创建通知管理器
func NewNotificationManager() *NotificationManager {
	return &NotificationManager{
		notifiers: make([]Notifier, 0),
	}
}

// RegisterNotifier 注册通知器
func (nm *NotificationManager) RegisterNotifier(notifier Notifier) {
	nm.mu.Lock()
	defer nm.mu.Unlock()

	nm.notifiers = append(nm.notifiers, notifier)
	log.Printf("Registered notifier: %s (enabled: %v)", notifier.GetNotifierType(), notifier.IsEnabled())
}

// SendNotification 发送通知到所有启用的通知器
func (nm *NotificationManager) SendNotification(ctx context.Context, message *NotificationMessage) error {
	nm.mu.RLock()
	defer nm.mu.RUnlock()

	if len(nm.notifiers) == 0 {
		log.Printf("No notifiers registered, skipping notification")
		return nil
	}

	var lastError error
	successCount := 0
	enabledCount := 0

	for _, notifier := range nm.notifiers {
		if !notifier.IsEnabled() {
			log.Printf("Notifier %s is disabled, skipping", notifier.GetNotifierType())
			continue
		}

		enabledCount++

		if err := notifier.SendNotification(ctx, message); err != nil {
			log.Printf("Failed to send notification via %s: %v", notifier.GetNotifierType(), err)
			lastError = err
		} else {
			successCount++
			log.Printf("Successfully sent notification via %s for service %s",
				notifier.GetNotifierType(), message.ServiceID)
		}
	}

	// 记录发送统计
	log.Printf("Notification sent: %d/%d enabled notifiers succeeded", successCount, enabledCount)

	// 如果没有启用的通知器，返回提示
	if enabledCount == 0 {
		return fmt.Errorf("no enabled notifiers available")
	}

	// 如果所有启用的通知器都失败了，返回最后一个错误
	if successCount == 0 && lastError != nil {
		return fmt.Errorf("all notifiers failed, last error: %w", lastError)
	}

	return nil
}

// SendServiceFailedNotification 发送服务失败通知的便捷方法
func (nm *NotificationManager) SendServiceFailedNotification(ctx context.Context, serviceID, serviceName, imageName, workerID, nodeIP, failureReason string) error {
	message := &NotificationMessage{
		Level:     NotificationLevelError,
		Title:     "服务部署失败",
		Content:   fmt.Sprintf("服务 %s (%s) 部署失败，请检查相关配置和资源", serviceName, serviceID),
		ServiceID: serviceID,
		Metadata: map[string]interface{}{
			"service_name":   serviceName,
			"image_name":     imageName,
			"worker_id":      workerID,
			"node_ip":        nodeIP,
			"failure_reason": failureReason,
		},
		Timestamp: GetCSTTimeString(), // 使用东八区时间
	}

	return nm.SendNotification(ctx, message)
}

// SendServiceSuccessNotification 发送服务成功通知的便捷方法
func (nm *NotificationManager) SendServiceSuccessNotification(ctx context.Context, serviceID, serviceName, content string) error {
	message := &NotificationMessage{
		Level:     NotificationLevelSuccess,
		Title:     "服务操作成功",
		Content:   content,
		ServiceID: serviceID,
		Metadata: map[string]interface{}{
			"service_name": serviceName,
		},
		Timestamp: GetCSTTimeString(), // 使用东八区时间
	}

	return nm.SendNotification(ctx, message)
}

// SendCustomNotification 发送自定义通知的便捷方法
func (nm *NotificationManager) SendCustomNotification(ctx context.Context, level NotificationLevel, title, content, serviceID string, metadata map[string]interface{}) error {
	message := &NotificationMessage{
		Level:     level,
		Title:     title,
		Content:   content,
		ServiceID: serviceID,
		Metadata:  metadata,
		Timestamp: GetCSTTimeString(), // 使用东八区时间
	}

	return nm.SendNotification(ctx, message)
}

// GetEnabledNotifiers 获取启用的通知器列表
func (nm *NotificationManager) GetEnabledNotifiers() []string {
	nm.mu.RLock()
	defer nm.mu.RUnlock()

	var enabled []string
	for _, notifier := range nm.notifiers {
		if notifier.IsEnabled() {
			enabled = append(enabled, notifier.GetNotifierType())
		}
	}
	return enabled
}

// GetNotifierCount 获取通知器数量统计
func (nm *NotificationManager) GetNotifierCount() (total, enabled int) {
	nm.mu.RLock()
	defer nm.mu.RUnlock()

	total = len(nm.notifiers)
	for _, notifier := range nm.notifiers {
		if notifier.IsEnabled() {
			enabled++
		}
	}
	return total, enabled
}

// SetNotifierEnabled 设置通知器启用状态（如果支持动态配置）
func (nm *NotificationManager) SetNotifierEnabled(notifierType string, enabled bool) error {
	nm.mu.Lock()
	defer nm.mu.Unlock()

	for _, notifier := range nm.notifiers {
		if notifier.GetNotifierType() == notifierType {
			// 这里需要通知器支持动态配置，当前实现中暂时不支持
			// 可以在未来扩展时添加 SetEnabled 方法到 Notifier 接口
			log.Printf("Found notifier %s, but dynamic configuration not supported yet", notifierType)
			return fmt.Errorf("dynamic configuration not supported for notifier: %s", notifierType)
		}
	}

	return fmt.Errorf("notifier not found: %s", notifierType)
}

// =============================================================================
// 全局配置和默认创建函数
// =============================================================================

// NotificationConfig 通知配置结构（避免循环导入）
type NotificationConfig struct {
	Feishu   FeishuNotificationConfig   `yaml:"feishu" json:"feishu"`
	WeChat   WeChatNotificationConfig   `yaml:"wechat" json:"wechat"`
	DingTalk DingTalkNotificationConfig `yaml:"dingtalk" json:"dingtalk"`
	Email    EmailNotificationConfig    `yaml:"email" json:"email"`
}

// FeishuNotificationConfig 飞书通知配置
type FeishuNotificationConfig struct {
	Enabled    bool              `yaml:"enabled" json:"enabled"`
	WebhookURL string            `yaml:"webhook_url" json:"webhook_url"`
	UserMap    map[string]string `yaml:"user_map" json:"user_map"`
}

// WeChatNotificationConfig 微信通知配置
type WeChatNotificationConfig struct {
	Enabled    bool   `yaml:"enabled" json:"enabled"`
	WebhookURL string `yaml:"webhook_url" json:"webhook_url"`
	AppID      string `yaml:"app_id" json:"app_id"`
	Secret     string `yaml:"secret" json:"secret"`
}

// DingTalkNotificationConfig 钉钉通知配置
type DingTalkNotificationConfig struct {
	Enabled     bool   `yaml:"enabled" json:"enabled"`
	WebhookURL  string `yaml:"webhook_url" json:"webhook_url"`
	AccessToken string `yaml:"access_token" json:"access_token"`
}

// EmailNotificationConfig 邮件通知配置
type EmailNotificationConfig struct {
	Enabled     bool     `yaml:"enabled" json:"enabled"`
	SMTPHost    string   `yaml:"smtp_host" json:"smtp_host"`
	SMTPPort    int      `yaml:"smtp_port" json:"smtp_port"`
	Username    string   `yaml:"username" json:"username"`
	Password    string   `yaml:"password" json:"password"`
	FromName    string   `yaml:"from_name" json:"from_name"`
	ToAddresses []string `yaml:"to_addresses" json:"to_addresses"`
}

// CreateNotificationManagerFromConfig 基于通知配置创建通知管理器
func CreateNotificationManagerFromConfig(notificationConfig NotificationConfig) *NotificationManager {
	manager := NewNotificationManager()

	// 🔍 根据配置决定是否启用飞书通知
	if notificationConfig.Feishu.Enabled {
		feishuConfig := FeishuConfig{
			WebhookURL: notificationConfig.Feishu.WebhookURL,
			Enabled:    notificationConfig.Feishu.Enabled,
			UserMap:    notificationConfig.Feishu.UserMap,
			Templates:  make(map[string]string),
			Timeout:    10 * time.Second,
		}
		feishuNotifier := NewFeishuNotifier(feishuConfig)
		manager.RegisterNotifier(feishuNotifier)
		log.Printf("✅ Feishu notifier enabled from config file")
	} else {
		log.Printf("❌ Feishu notifier disabled by config")
	}

	// 🔍 微信、钉钉、邮件通知器暂未实现，先记录配置状态
	if notificationConfig.WeChat.Enabled {
		log.Printf("⚠️  WeChat notifier enabled in config but not implemented yet")
	} else {
		log.Printf("❌ WeChat notifier disabled by config")
	}

	if notificationConfig.DingTalk.Enabled {
		log.Printf("⚠️  DingTalk notifier enabled in config but not implemented yet")
	} else {
		log.Printf("❌ DingTalk notifier disabled by config")
	}

	if notificationConfig.Email.Enabled {
		log.Printf("⚠️  Email notifier enabled in config but not implemented yet")
	} else {
		log.Printf("❌ Email notifier disabled by config")
	}

	return manager
}
