package service

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/zero-ops/service-system/internal/models"
	"github.com/zero-ops/service-system/internal/pkg/utils"
)

// CreateDeployRecord creates a new deployment record
func (s *Service) CreateDeployRecord(ctx context.Context, req *models.CreateServiceRequest) (*models.ServiceResponse, error) {

	// 如果请求参数中带有 serviceID，则使用该 ID
	var serviceID string
	var existingRecord models.DeployRecord
	var isExistingService bool

	if req.ServiceInfo.ServiceID != "" {
		// 使用请求中提供的 serviceID
		serviceID = req.ServiceInfo.ServiceID

		// 检查数据库连接
		if s.deployRecordRepo != nil {
			// 尝试从数据库获取现有记录
			var err error
			existingRecord, err = s.deployRecordRepo.GetByID(serviceID)
			if err == nil {
				// 找到了现有记录
				isExistingService = true
				log.Printf("Found existing service record with ID: %s", serviceID)

				// 检查服务状态，如果正在运行或处理中，直接返回
				if existingRecord.Status == "RUNNING" || existingRecord.Status == "PROCESSING" {
					log.Printf("Service %s is already %s, returning existing service info", serviceID, existingRecord.Status)
					return &models.ServiceResponse{
						Code: 200,
						Data: gin.H{
							"serviceId":     existingRecord.ServiceID,
							"domain_prefix": existingRecord.DomainPrefix,
							"domain_suffix": existingRecord.DomainSuffix,
							"status":        existingRecord.Status,
						},
						Msg: fmt.Sprintf("Service: 服务已存在且正在%s",
							map[string]string{
								"RUNNING":    "运行中",
								"PROCESSING": "处理中",
							}[existingRecord.Status]),
					}, nil
				}
			} else {
				log.Printf("Service ID provided but no existing record found: %v", err)
			}
		}
	} else {
		// 生成新的 serviceID
		var err error
		serviceID, err = utils.GeneratePrefixedULID("svc")
		if err != nil {
			log.Printf("Failed to generate ULID for service ID: %v", err)
			// Fallback to the old method if ULID generation fails
			serviceID = fmt.Sprintf("svc%d", time.Now().UnixNano())
		}
	}

	// 设置默认值
	// API 相关字段默认值
	apiReplica := req.ServiceInfo.APIReplica
	if apiReplica <= 0 {
		apiReplica = 1 // 默认值为 1
	}

	apiCpu := req.ServiceInfo.APICPU
	if apiCpu == 0 {
		apiCpu = 0.8 // 默认值为 1.0 CPU
	}

	apiMemory := req.ServiceInfo.APIMemory
	if apiMemory == 0 {
		apiMemory = 1024 // 默认值为 1024 MB 内存
	}

	// Auto 相关字段默认值
	autoReplica := req.ServiceInfo.AutoReplica
	if autoReplica <= 0 {
		autoReplica = 0 // 默认值为 1
	}

	autoCpu := req.ServiceInfo.AutoCPU
	if autoCpu == 0 {
		autoCpu = 0.8 // 默认值为 1.0 CPU
	}

	autoMemory := req.ServiceInfo.AutoMemory
	if autoMemory == 0 {
		autoMemory = 1024 // 默认值为 1024 MB 内存
	}

	// 计算 duration_seconds
	var durationSeconds int64
	if req.ServiceInfo.Expiration != "" {
		// 解析过期时间
		expirationTime, err := time.Parse(time.RFC3339, req.ServiceInfo.Expiration)
		if err != nil {
			log.Printf("Warning: Failed to parse expiration time: %v, using default duration", err)
			// 使用默认的持续时间（15天）
			durationSeconds = 15 * 24 * 60 * 60 // 15天的秒数
		} else {
			// 获取当前时间
			now := time.Now()
			// 计算持续时间（秒）
			durationSeconds = int64(expirationTime.Sub(now).Seconds())
			if durationSeconds < 0 {
				log.Printf("Warning: Expiration time is in the past, using default duration")
				durationSeconds = 15 * 24 * 60 * 60 // 15天的秒数
			}
		}
	} else {
		// 如果没有设置过期时间，使用默认的持续时间（15天）
		durationSeconds = 15 * 24 * 60 * 60 // 30天的秒数
	}

	var DomainPrefix string
	if req.ServiceInfo.DomainPrefix != "" {
		DomainPrefix = req.ServiceInfo.DomainPrefix
	} else {
		DomainPrefix = serviceID
	}

	// Create a deploy record
	deployRecord := models.DeployRecord{
		ServiceID:       serviceID,    // Use provided or generated service ID
		Name:            DomainPrefix, // Use domain as name for now
		ImageName:       req.ServiceInfo.ImageName,
		ServiceType:     req.ServiceInfo.ServiceType, // Use ServerType from request
		DomainPrefix:    DomainPrefix,
		DomainSuffix:    "",
		Expiration:      req.ServiceInfo.Expiration, // Use expiration from request
		DurationSeconds: durationSeconds,            // 设置持续时间（秒）
		Status:          "QUEUEING",                 // Initial status
		Labels:          req.ServiceInfo.Labels,
		CustomerEnvs:    req.ServiceInfo.CustomerEnvs, // 用户自定义环境变量
		Remark:          fmt.Sprintf("Created by %s (Label: %s)", serviceID, req.ServiceInfo.Labels),
		ApiReplica:      apiReplica,
		ApiCpu:          apiCpu,
		ApiMemory:       apiMemory,
		AutoReplica:     autoReplica,
		AutoCpu:         autoCpu,
		AutoMemory:      autoMemory,
	}

	// 如果是已存在的服务，保留原有的一些信息
	if isExistingService {
		// 保留原有的 WorkerID
		deployRecord.WorkerID = existingRecord.WorkerID
		// 如果考虑插件问题，需要保留 NodeIP
		// deployRecord.NodeIP = existingRecord.NodeIP

		// 更新备注信息
		deployRecord.Remark = fmt.Sprintf("Updated from existing service: %s (Label: %s)", serviceID, req.ServiceInfo.Labels)

		// 如果是已部署的服务，保持其状态
		if existingRecord.Status == "RUNNING" {
			deployRecord.Status = "RUNNING"
			deployRecord.NodeIP = existingRecord.NodeIP

			deployRecord.DomainSuffix = existingRecord.DomainSuffix
		}
	}

	// 存储到数据库
	if s.deployRecordRepo != nil {
		var err error
		if isExistingService {
			// 如果是已存在的服务，更新记录
			log.Printf("Updating existing service record with ID: %s", serviceID)
			// 保留原有记录的 ID
			deployRecord.ID = existingRecord.ID
			if err = s.deployRecordRepo.Update(deployRecord); err != nil {
				log.Printf("Failed to update deploy record in database: %v", err)
				return &models.ServiceResponse{
					Code: 500,
					Msg:  fmt.Sprintf("无法更新部署记录: %v", err),
				}, fmt.Errorf("无法更新部署记录: %w", err)
			}
		} else {
			// 创建新记录
			if err = s.deployRecordRepo.Create(&deployRecord); err != nil {
				log.Printf("Failed to store deploy record in database: %v", err)
				return &models.ServiceResponse{
					Code: 500,
					Msg:  fmt.Sprintf("无法创建部署记录: %v", err),
				}, fmt.Errorf("无法创建部署记录: %w", err)
			}
		}
	} else {
		// 如果没有数据库连接，返回错误
		return &models.ServiceResponse{
			Code: 500,
			Msg:  "数据库连接不可用",
		}, fmt.Errorf("数据库连接不可用")
	}

	// ===通知===
	if deployRecord.ServiceType != "TRIAL" {
		s.NotificationManager.SendNotification(context.Background(), &utils.NotificationMessage{
			Level:     utils.NotificationLevelError,
			Title:     "正式版部署请求",
			Content:   fmt.Sprintf("出现正式版部署请求, service_id：%s", deployRecord.ServiceID),
			ServiceID: deployRecord.ServiceID,
			Metadata:  nil,
			Timestamp: utils.GetCSTTimeString(),
		})
	}

	// 根据是否为现有服务设置不同的响应消息
	responseMsg := "Service: 创建服务成功"
	if isExistingService {
		responseMsg = "Service: 更新服务成功"
	}

	return &models.ServiceResponse{
		Code: 200,
		Data: gin.H{
			"serviceId": deployRecord.ServiceID,
			"updated":   isExistingService,
		},
		Msg: responseMsg,
	}, nil
}

// DeleteDeployRecord deletes a deployment record
func (s *Service) DeleteDeployRecord(ctx context.Context, service_id string) (*models.ServiceResponse, error) {
	// 检查数据库连接
	if s.deployRecordRepo == nil {
		return &models.ServiceResponse{
			Code: 500,
			Msg:  "数据库连接不可用",
		}, fmt.Errorf("数据库连接不可用")
	}

	// 从数据库获取部署记录
	record, err := s.deployRecordRepo.GetByID(service_id)
	if err != nil {
		return &models.ServiceResponse{
			Code: 404,
			Msg:  fmt.Sprintf("无法找到部署记录: %v", err),
		}, fmt.Errorf("无法找到部署记录: %w", err)
	}

	// 如果记录分配了 worker，先停止服务
	if record.WorkerID != "" {
		// 从数据库获取 worker 信息
		worker, err := s.workerRepo.GetByID(record.WorkerID)
		if err != nil {
			log.Printf("Failed to get worker from database: %v", err)
			// 如果链接数据库失败，需要终止后续操作
			return &models.ServiceResponse{
				Code: 500,
				Msg:  fmt.Sprintf("无法获取 worker 信息: %v", err),
			}, fmt.Errorf("无法获取 worker 信息: %w", err)
		}

		// 创建 worker 客户端
		workerClient := NewWorkerHTTPClient(worker.Host)

		// 创建请求对象
		workerReq := &models.WorkerDeployRequest{
			NodeIP:    record.NodeIP, // 使用 NodeIP
			ServiceId: service_id,    // 使用服务 ID
		}

		// 尝试在 worker 上停止容器
		if _, err := workerClient.StopContainer(ctx, workerReq); err != nil {
			log.Printf("Failed to stop container on worker: %v", err)
			// 如果停止失败，需要终止后续操作
			return &models.ServiceResponse{
				Code: 500,
				Msg:  fmt.Sprintf("无法停止容器: %v", err),
			}, fmt.Errorf("无法停止容器: %w", err)
		}
	}

	// 不直接删除记录，而是更新记录的状态为 PROCESSING
	// 这样可以确保 worker 层有足够的时间处理请求，避免数据不一致
	record.Status = "PROCESSING"
	record.Remark = "DELETE_REQUESTED" // 添加备注，表明这是一个删除请求
	record.UpdatedAt = utils.GetCSTTimeString()

	if err := s.deployRecordRepo.Update(record); err != nil {
		return &models.ServiceResponse{
			Code: 500,
			Msg:  fmt.Sprintf("无法更新部署记录状态: %v", err),
		}, fmt.Errorf("无法更新部署记录状态: %w", err)
	}

	return &models.ServiceResponse{
		Code: 200,
		Data: gin.H{
			"serviceId": service_id,
			"status":    "PROCESSING",
		},
		Msg: "Service: 删除服务请求已提交，正在处理中",
	}, nil
}

// GetDeployRecordList returns a list of deployment records with pagination and filtering
func (s *Service) GetDeployRecordList(ctx context.Context, req *models.DeployRecordListRequest) (*models.ServiceResponse, error) {
	// 检查数据库连接
	if s.deployRecordRepo == nil {
		return &models.ServiceResponse{
			Code: 500,
			Msg:  "数据库连接不可用",
		}, fmt.Errorf("数据库连接不可用")
	}

	// 设置默认值
	page := req.Page
	if page < 1 {
		page = 1
	}

	pageSize := req.PageSize
	if pageSize < 1 {
		pageSize = 10
	}

	// 从数据库获取部署记录
	records, total, err := s.deployRecordRepo.GetAll(page, pageSize, req.ServiceIDs)
	if err != nil {
		return &models.ServiceResponse{
			Code: 500,
			Msg:  fmt.Sprintf("无法获取部署记录列表: %v", err),
		}, fmt.Errorf("无法获取部署记录列表: %w", err)
	}

	// 转换为响应格式
	recordItems := make([]models.DeployRecordItem, len(records))
	for i, record := range records {
		recordItems[i] = models.DeployRecordItem{
			ID:           record.ID,
			ServiceID:    record.ServiceID,
			Name:         record.Name,
			DomainPrefix: record.DomainPrefix,
			DomainSuffix: record.DomainSuffix,
			ImageName:    record.ImageName,
			ImageURL:     record.ImageURL,
			ServiceType:  record.ServiceType,
			Expiration:   record.Expiration,
			Status:       record.Status,
			Labels:       record.Labels,
			CustomerEnvs: record.CustomerEnvs,
			WorkerID:     record.WorkerID,
			NodeIP:       record.NodeIP,
			ApiReplica:   record.ApiReplica,
			ApiCpu:       record.ApiCpu,
			ApiMemory:    record.ApiMemory,
			AutoReplica:  record.AutoReplica,
			AutoCpu:      record.AutoCpu,
			AutoMemory:   record.AutoMemory,
			CreatedAt:    record.CreatedAt,
			UpdatedAt:    record.UpdatedAt,
		}
	}

	// 构建基础响应数据
	responseData := gin.H{
		"total":     total,
		"page":      page,
		"page_size": pageSize,
		"records":   recordItems,
	}

	// 如果传递了service_id且个数≤10，批量计算排队位置信息
	if len(req.ServiceIDs) > 0 && len(req.ServiceIDs) <= 10 {
		queueInfos, err := s.deployRecordRepo.GetBatchQueuePositions(req.ServiceIDs)

		if err != nil {
			// 记录错误但不影响主要功能
			log.Printf("Failed to get batch queue positions for services %v: %v", req.ServiceIDs, err)
		} else if len(queueInfos) > 0 {
			// 只有当有服务在排队中时才添加排队信息
			responseData["queue_infos"] = queueInfos
			// 提取有排队信息的服务ID列表用于日志
			serviceIDsWithQueue := make([]string, 0, len(queueInfos))
			for serviceID := range queueInfos {
				serviceIDsWithQueue = append(serviceIDsWithQueue, serviceID)
			}
			log.Printf("Retrieved queue positions for %d services: %v", len(queueInfos), serviceIDsWithQueue)
		}
	}

	return &models.ServiceResponse{
		Code: 200,
		Data: responseData,
		Msg:  "Service: 获取服务列表成功",
	}, nil
}
