#!/bin/bash

# =============================================================================
# Docker 镜像合并脚本
# 功能：从镜像B中提取插件文件，合并到基础镜像A中，生成新镜像A'
# =============================================================================

set -euo pipefail  # 严格模式：遇到错误立即退出

# =============================================================================
# 配置参数
# =============================================================================

# 默认配置
DEFAULT_BASE_IMAGE=""           # 基础镜像A（无插件）
DEFAULT_PLUGIN_SOURCE_IMAGE=""  # 插件源镜像B
DEFAULT_OUTPUT_IMAGE=""         # 输出镜像A'
DEFAULT_WORK_DIR="/tmp/docker_merge_$$"  # 工作目录
DEFAULT_CONTAINER_PATH="/var/www/huanpingtong-server"  # 容器内路径

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# =============================================================================
# 工具函数
# =============================================================================

# 打印带颜色的日志
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
Docker 镜像合并脚本

用法: $0 [选项]

选项:
    -a, --base-image IMAGE         基础镜像A（无插件）
    -b, --plugin-source IMAGE      插件源镜像B
    -o, --output-image IMAGE       输出镜像A'
    -w, --work-dir DIR             工作目录（默认: /tmp/docker_merge_\$\$）
    -p, --container-path PATH      容器内路径（默认: /var/www/huanpingtong-server）
    -f, --force                    强制覆盖已存在的镜像
    -c, --cleanup                  完成后清理工作目录
    -v, --verbose                  详细输出
    -h, --help                     显示此帮助信息

示例:
    $0 -a myapp:base -b myapp:with-plugins -o myapp:merged
    $0 --base-image myapp:base --plugin-source myapp:plugins --output-image myapp:final --cleanup

环境变量:
    BASE_IMAGE                     基础镜像A
    PLUGIN_SOURCE_IMAGE            插件源镜像B  
    OUTPUT_IMAGE                   输出镜像A'
    WORK_DIR                       工作目录
    CONTAINER_PATH                 容器内路径
EOF
}

# 检查必要的工具
check_dependencies() {
    local missing_tools=()
    
    for tool in docker; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_error "缺少必要工具: ${missing_tools[*]}"
        log_error "请安装缺少的工具后重试"
        exit 1
    fi
}

# 检查 Docker 是否运行
check_docker() {
    if ! docker info &> /dev/null; then
        log_error "Docker 未运行或无权限访问"
        log_error "请确保 Docker 服务已启动且当前用户有权限访问"
        exit 1
    fi
}

# 检查镜像是否存在
check_image_exists() {
    local image="$1"
    if ! docker image inspect "$image" &> /dev/null; then
        log_error "镜像不存在: $image"
        return 1
    fi
    return 0
}

# 全局变量用于清理函数
GLOBAL_WORK_DIR=""
GLOBAL_CLEANUP_FLAG="false"

# 清理函数
cleanup() {
    # 使用全局变量，避免参数传递问题
    local work_dir="${GLOBAL_WORK_DIR:-}"
    local cleanup_flag="${GLOBAL_CLEANUP_FLAG:-false}"

    # 清理临时容器（总是执行）
    local temp_containers=$(docker ps -a --filter "label=docker-merge-temp" --format "{{.ID}}" 2>/dev/null || true)
    if [ -n "$temp_containers" ]; then
        log_info "清理临时容器"
        echo "$temp_containers" | xargs docker rm -f &> /dev/null || true
    fi

    # 清理工作目录（仅在启用清理标志时）
    if [ "$cleanup_flag" = "true" ] && [ -n "$work_dir" ] && [ -d "$work_dir" ]; then
        log_info "清理工作目录: $work_dir"
        rm -rf "$work_dir"
    fi
}

# =============================================================================
# 主要功能函数
# =============================================================================

# 从镜像B中提取插件文件
extract_plugins_from_image() {
    local source_image="$1"
    local work_dir="$2"
    local container_path="$3"
    local verbose="$4"
    
    log_info "从镜像 $source_image 中提取插件文件..."
    
    # 创建临时容器
    local container_id
    container_id=$(docker create --label docker-merge-temp "$source_image")
    
    if [ "$verbose" = "true" ]; then
        log_info "创建临时容器: $container_id"
    fi
    
    # 创建提取目录
    local extract_dir="$work_dir/extracted"
    mkdir -p "$extract_dir"
    
    # 从容器中复制文件
    if docker cp "$container_id:$container_path" "$extract_dir/" 2>/dev/null; then
        log_success "成功提取文件到: $extract_dir"
    else
        log_error "从容器中复制文件失败"
        docker rm "$container_id" &> /dev/null || true
        return 1
    fi
    
    # 清理临时容器
    docker rm "$container_id" &> /dev/null || true
    
    return 0
}

# 过滤插件文件
filter_plugin_files() {
    local extract_dir="$1"
    local verbose="$2"
    
    log_info "过滤插件文件（保留 plugin_ 相关文件）..."
    
    local app_dir="$extract_dir/huanpingtong-server/app"
    
    if [ ! -d "$app_dir" ]; then
        log_error "未找到应用目录: $app_dir"
        return 1
    fi
    
    # 执行过滤命令：删除非插件文件
    cd "$extract_dir"
    
    if [ "$verbose" = "true" ]; then
        log_info "执行命令: find huanpingtong-server/app -depth ! -regex \".*plugin_.*\" -delete"
        find huanpingtong-server/app -depth ! -regex ".*plugin_.*" -print
        echo "--- 以上文件将被删除 ---"
    fi
    
    find huanpingtong-server/app -depth ! -regex ".*plugin_.*" -delete
    
    # 显示剩余的插件文件
    if [ "$verbose" = "true" ]; then
        log_info "剩余的插件文件:"
        find huanpingtong-server/app -type f 2>/dev/null || true
    fi
    
    log_success "插件文件过滤完成"
    return 0
}

# 构建新镜像
build_merged_image() {
    local base_image="$1"
    local work_dir="$2"
    local output_image="$3"
    local container_path="$4"
    local force="$5"
    local verbose="$6"
    
    log_info "构建合并后的镜像: $output_image"
    
    # 检查输出镜像是否已存在
    if [ "$force" != "true" ] && docker image inspect "$output_image" &> /dev/null; then
        log_error "输出镜像已存在: $output_image"
        log_error "使用 --force 参数强制覆盖"
        return 1
    fi
    
    # 创建 Dockerfile
    local dockerfile="$work_dir/Dockerfile"
    cat > "$dockerfile" << EOF
FROM $base_image

# 复制插件文件
COPY extracted/huanpingtong-server $container_path

# 设置权限（如果需要）
RUN chown -R www-data:www-data $container_path 2>/dev/null || true

# 添加标签
LABEL docker-merge.base-image="$base_image"
LABEL docker-merge.created="$(date -u +%Y-%m-%dT%H:%M:%SZ)"
LABEL docker-merge.version="1.0"
EOF
    
    if [ "$verbose" = "true" ]; then
        log_info "生成的 Dockerfile:"
        cat "$dockerfile"
        echo "---"
    fi
    
    # 构建镜像
    cd "$work_dir"
    
    local build_args=""
    if [ "$verbose" != "true" ]; then
        build_args="--quiet"
    fi
    
    if docker build $build_args -t "$output_image" .; then
        log_success "镜像构建成功: $output_image"
        
        # 显示镜像信息
        log_info "新镜像信息:"
        local image_id=$(docker image inspect "$output_image" --format "{{.Id}}" | head -c 12)
        local image_size=$(docker image inspect "$output_image" --format "{{.Size}}")
        echo "  镜像ID: $image_id"
        echo "  镜像大小: $image_size bytes"
        
        return 0
    else
        log_error "镜像构建失败"
        return 1
    fi
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    # 默认参数
    local base_image="$DEFAULT_BASE_IMAGE"
    local plugin_source_image="$DEFAULT_PLUGIN_SOURCE_IMAGE"
    local output_image="$DEFAULT_OUTPUT_IMAGE"
    local work_dir="$DEFAULT_WORK_DIR"
    local container_path="$DEFAULT_CONTAINER_PATH"
    local force="false"
    local cleanup_flag="false"
    local verbose="false"
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -a|--base-image)
                base_image="$2"
                shift 2
                ;;
            -b|--plugin-source)
                plugin_source_image="$2"
                shift 2
                ;;
            -o|--output-image)
                output_image="$2"
                shift 2
                ;;
            -w|--work-dir)
                work_dir="$2"
                shift 2
                ;;
            -p|--container-path)
                container_path="$2"
                shift 2
                ;;
            -f|--force)
                force="true"
                shift
                ;;
            -c|--cleanup)
                cleanup_flag="true"
                shift
                ;;
            -v|--verbose)
                verbose="true"
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 从环境变量获取配置（如果命令行未指定）
    base_image="${base_image:-$BASE_IMAGE}"
    plugin_source_image="${plugin_source_image:-$PLUGIN_SOURCE_IMAGE}"
    output_image="${output_image:-$OUTPUT_IMAGE}"
    work_dir="${work_dir:-$WORK_DIR}"
    container_path="${container_path:-$CONTAINER_PATH}"
    
    # 验证必要参数
    if [ -z "$base_image" ] || [ -z "$plugin_source_image" ] || [ -z "$output_image" ]; then
        log_error "缺少必要参数"
        log_error "必须指定: --base-image, --plugin-source, --output-image"
        show_help
        exit 1
    fi

    # 设置全局变量供清理函数使用
    GLOBAL_WORK_DIR="$work_dir"
    GLOBAL_CLEANUP_FLAG="$cleanup_flag"

    # 设置清理陷阱
    trap 'cleanup' EXIT
    
    log_info "开始 Docker 镜像合并流程"
    log_info "基础镜像: $base_image"
    log_info "插件源镜像: $plugin_source_image"
    log_info "输出镜像: $output_image"
    log_info "工作目录: $work_dir"
    
    # 检查依赖
    check_dependencies
    check_docker
    
    # 检查镜像是否存在
    check_image_exists "$base_image" || exit 1
    check_image_exists "$plugin_source_image" || exit 1
    
    # 创建工作目录
    mkdir -p "$work_dir"
    
    # 执行主要流程
    extract_plugins_from_image "$plugin_source_image" "$work_dir" "$container_path" "$verbose" || exit 1
    filter_plugin_files "$work_dir/extracted" "$verbose" || exit 1
    build_merged_image "$base_image" "$work_dir" "$output_image" "$container_path" "$force" "$verbose" || exit 1
    
    log_success "镜像合并完成！"
    log_success "新镜像: $output_image"
}

# 执行主函数
main "$@"
