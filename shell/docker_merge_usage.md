# Docker 镜像合并脚本使用指南

## 📋 功能概述

这个脚本实现了以下流程：
1. 从基础镜像A（无插件）开始
2. 从插件源镜像B中提取插件文件
3. 过滤文件，只保留 `plugin_` 相关的文件
4. 将过滤后的插件文件合并到基础镜像A中
5. 生成新的完整镜像A'

## 🚀 快速开始

### 基本用法

```bash
# 基本合并命令
./docker_image_merge.sh \
  --base-image myapp:base \
  --plugin-source myapp:with-plugins \
  --output-image myapp:merged

# 带清理的完整命令
./docker_image_merge.sh \
  -a myapp:base \
  -b myapp:plugins \
  -o myapp:final \
  --cleanup \
  --verbose
```

### 使用环境变量

```bash
# 设置环境变量
export BASE_IMAGE="myapp:base"
export PLUGIN_SOURCE_IMAGE="myapp:with-plugins"
export OUTPUT_IMAGE="myapp:merged"

# 直接运行
./docker_image_merge.sh --cleanup
```

## 📖 参数说明

| 参数 | 短参数 | 说明 | 必需 |
|------|--------|------|------|
| `--base-image` | `-a` | 基础镜像A（无插件） | ✅ |
| `--plugin-source` | `-b` | 插件源镜像B | ✅ |
| `--output-image` | `-o` | 输出镜像A' | ✅ |
| `--work-dir` | `-w` | 工作目录 | ❌ |
| `--container-path` | `-p` | 容器内路径 | ❌ |
| `--force` | `-f` | 强制覆盖已存在的镜像 | ❌ |
| `--cleanup` | `-c` | 完成后清理工作目录 | ❌ |
| `--verbose` | `-v` | 详细输出 | ❌ |
| `--help` | `-h` | 显示帮助信息 | ❌ |

## 🔧 使用场景示例

### 场景1：CI/CD 流水线中使用

```bash
#!/bin/bash
# CI/CD 脚本示例

# 构建基础镜像（无插件）
docker build -t myapp:base-${BUILD_NUMBER} .

# 合并插件
./docker_image_merge.sh \
  --base-image myapp:base-${BUILD_NUMBER} \
  --plugin-source myapp:plugins-latest \
  --output-image myapp:release-${BUILD_NUMBER} \
  --force \
  --cleanup \
  --verbose

# 推送到仓库
docker push myapp:release-${BUILD_NUMBER}
```

### 场景2：本地开发测试

```bash
#!/bin/bash
# 本地开发脚本

# 设置本地镜像
BASE_IMAGE="localhost:5000/myapp:dev"
PLUGIN_SOURCE="localhost:5000/myapp:plugins"
OUTPUT_IMAGE="localhost:5000/myapp:test"

./docker_image_merge.sh \
  -a "$BASE_IMAGE" \
  -b "$PLUGIN_SOURCE" \
  -o "$OUTPUT_IMAGE" \
  --verbose \
  --cleanup

# 启动测试容器
docker run -d --name test-container "$OUTPUT_IMAGE"
```

### 场景3：批量处理多个版本

```bash
#!/bin/bash
# 批量处理脚本

VERSIONS=("1.0.0" "1.1.0" "1.2.0")
PLUGIN_SOURCE="myapp:plugins-latest"

for version in "${VERSIONS[@]}"; do
  echo "处理版本: $version"
  
  ./docker_image_merge.sh \
    --base-image "myapp:base-$version" \
    --plugin-source "$PLUGIN_SOURCE" \
    --output-image "myapp:complete-$version" \
    --force \
    --cleanup
    
  if [ $? -eq 0 ]; then
    echo "✅ 版本 $version 处理成功"
  else
    echo "❌ 版本 $version 处理失败"
  fi
done
```

## 🔍 工作原理详解

### 1. 文件提取阶段
```bash
# 创建临时容器
container_id=$(docker create plugin-source-image)

# 复制文件
docker cp $container_id:/var/www/huanpingtong-server ./extracted/

# 清理临时容器
docker rm $container_id
```

### 2. 文件过滤阶段
```bash
# 进入提取目录
cd extracted/

# 执行过滤命令（保留 plugin_ 相关文件）
find huanpingtong-server/app -depth ! -regex ".*plugin_.*" -delete
```

### 3. 镜像构建阶段
```dockerfile
FROM base-image

# 复制过滤后的插件文件
COPY extracted/huanpingtong-server /var/www/huanpingtong-server

# 设置权限
RUN chown -R www-data:www-data /var/www/huanpingtong-server

# 添加标签
LABEL docker-merge.base-image="base-image"
LABEL docker-merge.created="2024-01-01T00:00:00Z"
```

## ⚠️ 注意事项

### 权限问题
- 确保当前用户有 Docker 操作权限
- 脚本会自动设置文件权限为 `www-data:www-data`

### 磁盘空间
- 工作目录需要足够空间存储提取的文件
- 建议使用 `--cleanup` 参数自动清理

### 镜像覆盖
- 默认不会覆盖已存在的输出镜像
- 使用 `--force` 参数强制覆盖

### 网络问题
- 确保能够访问镜像仓库
- 大镜像可能需要较长时间

## 🐛 故障排除

### 常见错误

#### 1. 镜像不存在
```
[ERROR] 镜像不存在: myapp:base
```
**解决方案**：检查镜像名称和标签是否正确

#### 2. Docker 权限问题
```
[ERROR] Docker 未运行或无权限访问
```
**解决方案**：
```bash
# 启动 Docker 服务
sudo systemctl start docker

# 添加用户到 docker 组
sudo usermod -aG docker $USER
```

#### 3. 磁盘空间不足
```
[ERROR] 镜像构建失败
```
**解决方案**：清理 Docker 缓存
```bash
docker system prune -f
docker image prune -f
```

#### 4. 文件路径问题
```
[ERROR] 未找到应用目录
```
**解决方案**：检查容器内路径是否正确
```bash
# 检查镜像内部结构
docker run --rm -it image-name ls -la /var/www/
```

## 📊 性能优化建议

### 1. 使用本地镜像仓库
```bash
# 使用本地 registry 减少网络传输
docker run -d -p 5000:5000 --name registry registry:2
```

### 2. 并行处理
```bash
# 多个版本并行处理
for version in "${VERSIONS[@]}"; do
  (
    ./docker_image_merge.sh \
      --base-image "myapp:base-$version" \
      --plugin-source "$PLUGIN_SOURCE" \
      --output-image "myapp:complete-$version"
  ) &
done
wait
```

### 3. 缓存优化
```bash
# 使用 BuildKit 加速构建
export DOCKER_BUILDKIT=1
./docker_image_merge.sh ...
```

## 🔧 自定义扩展

### 修改过滤规则
如果需要修改文件过滤规则，编辑脚本中的 `filter_plugin_files` 函数：

```bash
# 原始规则：只保留 plugin_ 相关文件
find huanpingtong-server/app -depth ! -regex ".*plugin_.*" -delete

# 自定义规则示例：
# 保留 plugin_ 和 module_ 相关文件
find huanpingtong-server/app -depth ! -regex ".*\(plugin_\|module_\).*" -delete

# 保留特定目录
find huanpingtong-server/app -depth ! -path "*/plugins/*" ! -path "*/modules/*" -delete
```

### 添加后处理步骤
在 `build_merged_image` 函数的 Dockerfile 中添加自定义步骤：

```dockerfile
# 安装额外依赖
RUN apt-get update && apt-get install -y some-package

# 执行自定义脚本
RUN /path/to/custom-script.sh

# 设置环境变量
ENV PLUGIN_MODE=enabled
```

这个脚本提供了完整的镜像合并功能，具有良好的错误处理、日志输出和清理机制。你可以根据具体需求进行调整和扩展。
